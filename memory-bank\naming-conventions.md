# GoMyHire 移动端快速透视分析 - 命名规范文档

## 项目命名规范总览

### 全局命名空间结构
- **主命名空间**: `SmartOffice` - 全局应用命名空间
- **模块前缀**: 所有组件和功能使用 `smartoffice-` 前缀
- **文件命名**: 小写字母 + 连字符，如 `smartoffice-config-list.js`
- **构造函数**: 大驼峰命名，如 `ConfigListComponent`
- **方法和属性**: 小驼峰命名，如 `loadConfigs`

## 核心模块命名表

### SmartOffice.Core - 核心基础设施
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Core.EventBus | 类 | 全局事件总线系统 | src/js/core/smartoffice-events.js | 无 |
| SmartOffice.Core.Storage | 类 | 本地存储管理器 | src/js/core/smartoffice-storage.js | 无 |
| SmartOffice.Core.Router | 类 | 页面路由管理器 | src/js/core/smartoffice-router.js | EventBus, DOM |
| SmartOffice.Core.App | 类 | 应用主控制器 | src/js/core/smartoffice-app.js | 所有核心模块 |

### SmartOffice.Components - UI组件
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Components.ConfigList | 类 | 配置列表组件 | src/js/components/smartoffice-config-list.js | Storage, EventBus, DOM |
| SmartOffice.Components.ConfigForm | 类 | 配置表单组件 | src/js/components/smartoffice-config-form.js | Storage, EventBus, DOM |
| SmartOffice.Components.Dropdown | 类 | 移动端下拉菜单 | src/js/components/smartoffice-dropdown.js | DOM, Events |
| SmartOffice.Components.DataTable | 类 | 数据表格显示组件 | src/js/components/smartoffice-data-table.js | DOM, Format |
| SmartOffice.Components.FileUpload | 类 | 文件上传组件 | src/js/components/smartoffice-file-upload.js | EventBus, DataValidator |
| SmartOffice.Components.FieldSelector | 类 | 字段选择器组件 | src/js/components/smartoffice-field-selector.js | DOM, Events |
| SmartOffice.Components.Loading | 类 | 加载状态组件 | src/js/components/smartoffice-loading.js | DOM |

### SmartOffice.Data - 数据处理模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Data.CSVParser | 类 | CSV文件解析器 | src/js/data/smartoffice-csv-parser.js | DataValidator |
| SmartOffice.Data.PivotEngine | 类 | 透视表计算引擎 | src/js/data/smartoffice-pivot-engine.js | Helpers |
| SmartOffice.Data.ConfigManager | 类 | 配置管理器 | src/js/data/smartoffice-config-manager.js | Storage |
| SmartOffice.Data.DataValidator | 类 | 数据验证器 | src/js/data/smartoffice-data-validator.js | 无 |

### SmartOffice.Utils - 工具函数模块
| 名称 | 类型 | 用途 | 文件位置 | 依赖关系 |
|------|------|------|----------|----------|
| SmartOffice.Utils.Helpers | 对象 | 通用工具函数集合 | src/js/utils/smartoffice-helpers.js | 无 |
| SmartOffice.Utils.DOM | 对象 | DOM操作工具集合 | src/js/utils/smartoffice-dom.js | 无 |
| SmartOffice.Utils.Format | 对象 | 数据格式化工具 | src/js/utils/smartoffice-format.js | 无 |

## 事件命名规范

### SmartOffice.Events - 标准事件名称
| 事件名称 | 用途 | 触发时机 | 参数 |
|----------|------|----------|------|
| CONFIG_CREATE | 配置创建 | 新建配置保存时 | config对象 |
| CONFIG_UPDATE | 配置更新 | 编辑配置保存时 | config对象 |
| CONFIG_DELETE | 配置删除 | 删除配置时 | configId |
| FILE_UPLOAD_START | 文件上传开始 | 选择文件后 | file对象 |
| FILE_UPLOAD_PROGRESS | 文件上传进度 | 上传过程中 | progress百分比 |
| FILE_UPLOAD_COMPLETE | 文件上传完成 | 文件解析完成 | 解析后的数据 |
| DATA_PARSE_START | 数据解析开始 | 开始解析文件 | file对象 |
| DATA_PARSE_COMPLETE | 数据解析完成 | 解析完成 | 解析结果 |
| PIVOT_CALCULATE_START | 透视表计算开始 | 开始计算 | config对象 |
| PIVOT_CALCULATE_COMPLETE | 透视表计算完成 | 计算完成 | 结果数据 |

## 存储键名规范

### SmartOffice.StorageKeys - 标准存储键
| 键名 | 用途 | 数据类型 | 示例值 |
|------|------|----------|--------|
| configs | 透视表配置列表 | Array | [config1, config2] |
| currentConfig | 当前编辑配置 | Object | {id, name, fields} |
| appSettings | 应用设置 | Object | {theme, language} |
| userPrefs | 用户偏好 | Object | {autoSave, notifications} |
| lastUpload | 最后上传数据信息 | Object | {filename, timestamp} |

## CSS类命名规范

### 组件样式类
- **前缀**: 所有样式类使用 `so-` 前缀 (SmartOffice缩写)
- **组件类**: `so-config-list`, `so-config-form`, `so-dropdown`
- **状态类**: `so-active`, `so-disabled`, `so-loading`, `so-error`
- **iOS风格类**: `so-ios-card`, `so-ios-button`, `so-ios-input`

### 页面和布局类
- **页面类**: `page`, `page-hidden`, `page-content`
- **导航类**: `nav-bar`, `nav-title`, `nav-button`, `nav-back`, `nav-add`
- **容器类**: `app-container`, `main-content`, `config-list`

## 函数命名规范

### 生命周期函数
- **初始化**: `init()` - 组件初始化
- **渲染**: `render()` - 渲染界面
- **销毁**: `destroy()` - 清理资源
- **事件绑定**: `bindEvents()` - 绑定事件监听器

### 数据操作函数
- **加载**: `load*()` - 如 `loadConfigs()`, `loadData()`
- **保存**: `save*()` - 如 `saveConfig()`, `saveData()`
- **创建**: `create*()` - 如 `createConfig()`, `createCard()`
- **更新**: `update*()` - 如 `updateConfig()`, `updateUI()`
- **删除**: `delete*()` - 如 `deleteConfig()`, `deleteCard()`

### 事件处理函数
- **点击事件**: `on*Click()` - 如 `onAddClick()`, `onSaveClick()`
- **变更事件**: `on*Change()` - 如 `onFieldChange()`, `onValueChange()`
- **页面事件**: `onEnter*()`, `onLeave*()` - 如 `onEnterConfigForm()`

## 变量命名规范

### 实例变量
- **DOM元素**: `*Element` - 如 `containerElement`, `buttonElement`
- **数据集合**: `*s` - 如 `configs`, `fields`, `results`
- **当前项**: `current*` - 如 `currentConfig`, `currentData`
- **状态标志**: `is*` - 如 `isLoading`, `isEditing`, `isValid`

### 配置对象属性
- **基础信息**: `id`, `name`, `description`, `createdAt`, `updatedAt`
- **字段配置**: `rowFields`, `columnFields`, `valueFields`, `filterFields`
- **聚合设置**: `aggregationType` - 值为 'sum', 'count', 'average', 'min', 'max'

## 防重复命名检查清单

### 禁止使用的通用词汇
- ❌ `unified`, `mapping`, `converter`, `helper` (过于通用)
- ❌ `manager`, `handler`, `processor` (除非有明确业务前缀)
- ❌ `utils`, `common`, `shared` (必须加业务前缀)

### 推荐的业务前缀
- ✅ `config*` - 配置相关功能
- ✅ `pivot*` - 透视表相关功能
- ✅ `file*` - 文件处理相关功能
- ✅ `data*` - 数据处理相关功能
- ✅ `ui*` - 界面相关功能

## 更新记录

- **2025-01-03**: 初始创建命名规范文档
- **2025-01-03**: 记录所有已实现组件的命名规范
- **2025-01-03**: 建立事件、存储、CSS类的命名标准
- **2025-01-03 最终更新**: 添加字段选择器组件命名规范，完成95%组件的命名记录
