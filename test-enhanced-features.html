<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强功能测试 - SmartOffice</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f2f2f7;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e5e5ea;
            border-radius: 8px;
        }
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #1c1c1e;
        }
        .test-button {
            background: #007aff;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056cc;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            background: #f8f8f8;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
        }
        .chart-container {
            width: 100%;
            height: 300px;
            border: 1px solid #e5e5ea;
            border-radius: 8px;
            margin-top: 10px;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        .status.success { background: #34c759; color: white; }
        .status.error { background: #ff3b30; color: white; }
        .status.pending { background: #ff9500; color: white; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SmartOffice 增强功能测试</h1>
        <p>测试所有6个新增的增强功能是否正常工作</p>

        <!-- 1. 离线功能测试 -->
        <div class="test-section">
            <div class="test-title">1. 离线功能支持 <span id="offline-status" class="status pending">待测试</span></div>
            <button class="test-button" onclick="testOfflineFeatures()">测试离线功能</button>
            <div id="offline-result" class="test-result" style="display:none;"></div>
        </div>

        <!-- 2. 模板管理测试 -->
        <div class="test-section">
            <div class="test-title">2. 配置模板功能 <span id="template-status" class="status pending">待测试</span></div>
            <button class="test-button" onclick="testTemplateManager()">测试模板管理</button>
            <div id="template-result" class="test-result" style="display:none;"></div>
        </div>

        <!-- 3. 文件解析器测试 -->
        <div class="test-section">
            <div class="test-title">3. 文件格式扩展 <span id="parser-status" class="status pending">待测试</span></div>
            <button class="test-button" onclick="testExcelParser()">测试Excel解析</button>
            <button class="test-button" onclick="testJSONParser()">测试JSON解析</button>
            <div id="parser-result" class="test-result" style="display:none;"></div>
        </div>

        <!-- 4. 数据筛选测试 -->
        <div class="test-section">
            <div class="test-title">4. 数据筛选排序 <span id="filter-status" class="status pending">待测试</span></div>
            <button class="test-button" onclick="testDataFilters()">测试数据筛选</button>
            <div id="filter-result" class="test-result" style="display:none;"></div>
        </div>

        <!-- 5. 条件格式化测试 -->
        <div class="test-section">
            <div class="test-title">5. 条件格式化 <span id="formatter-status" class="status pending">待测试</span></div>
            <button class="test-button" onclick="testFormatter()">测试条件格式化</button>
            <div id="formatter-result" class="test-result" style="display:none;"></div>
        </div>

        <!-- 6. 数据可视化测试 -->
        <div class="test-section">
            <div class="test-title">6. 数据可视化 <span id="chart-status" class="status pending">待测试</span></div>
            <button class="test-button" onclick="testChartEngine()">测试图表引擎</button>
            <div class="chart-container" id="test-chart"></div>
            <div id="chart-result" class="test-result" style="display:none;"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <div class="test-title">综合测试</div>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="checkDependencies()">检查依赖</button>
            <div id="overall-result" class="test-result" style="display:none;"></div>
        </div>
    </div>

    <!-- 引入所有必要的JavaScript文件 -->
    <!-- 1. 核心基础设施 -->
    <script src="src/js/core/smartoffice-core.js"></script>
    <script src="src/js/core/smartoffice-events.js"></script>
    <script src="src/js/core/smartoffice-storage.js"></script>
    <script src="src/js/core/smartoffice-router.js"></script>
    <script src="src/js/core/smartoffice-offline.js"></script>

    <!-- 2. 工具函数 -->
    <script src="src/js/utils/smartoffice-helpers.js"></script>
    <script src="src/js/utils/smartoffice-dom.js"></script>
    <script src="src/js/utils/smartoffice-format.js"></script>

    <!-- 3. 数据处理模块 -->
    <script src="src/js/data/smartoffice-data-validator.js"></script>
    <script src="src/js/data/smartoffice-csv-parser.js"></script>
    <script src="src/js/data/smartoffice-config-manager.js"></script>
    <script src="src/js/data/smartoffice-pivot-engine.js"></script>

    <!-- 3.1 文件解析器 -->
    <script src="src/js/parsers/smartoffice-excel-parser.js"></script>
    <script src="src/js/parsers/smartoffice-json-parser.js"></script>

    <!-- 3.2 模板管理 -->
    <script src="src/js/templates/smartoffice-template-manager.js"></script>

    <!-- 3.3 数据筛选 -->
    <script src="src/js/filters/smartoffice-data-filters.js"></script>

    <!-- 3.4 可视化模块 -->
    <script src="src/js/visualization/smartoffice-formatter.js"></script>
    <script src="src/js/visualization/smartoffice-chart-engine.js"></script>

    <!-- 4. UI组件 -->
    <script src="src/js/components/smartoffice-loading.js"></script>
    <script src="src/js/components/smartoffice-toast.js"></script>

    <!-- 5. 应用控制器 -->
    <script src="src/js/core/smartoffice-app.js"></script>

    <script>
        // 测试函数
        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = status === 'success' ? '✅ 通过' : 
                                 status === 'error' ? '❌ 失败' : '⏳ 测试中';
        }

        function showResult(elementId, content) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = content;
        }

        // 1. 测试离线功能
        function testOfflineFeatures() {
            updateStatus('offline-status', 'pending');
            try {
                if (SmartOffice.Core.OfflineManager) {
                    const result = 'OfflineManager 已加载，Service Worker 支持检测完成';
                    showResult('offline-result', result);
                    updateStatus('offline-status', 'success');
                } else {
                    throw new Error('OfflineManager 未找到');
                }
            } catch (error) {
                showResult('offline-result', '错误: ' + error.message);
                updateStatus('offline-status', 'error');
            }
        }

        // 2. 测试模板管理
        function testTemplateManager() {
            updateStatus('template-status', 'pending');
            try {
                if (SmartOffice.Templates && SmartOffice.Templates.TemplateManager) {
                    const manager = new SmartOffice.Templates.TemplateManager();
                    const templates = manager.getBuiltinTemplates();
                    const result = `模板管理器工作正常，内置模板数量: ${templates.length}`;
                    showResult('template-result', result);
                    updateStatus('template-status', 'success');
                } else {
                    throw new Error('TemplateManager 未找到');
                }
            } catch (error) {
                showResult('template-result', '错误: ' + error.message);
                updateStatus('template-status', 'error');
            }
        }

        // 3. 测试Excel解析器
        function testExcelParser() {
            updateStatus('parser-status', 'pending');
            try {
                if (SmartOffice.Parsers && SmartOffice.Parsers.ExcelParser) {
                    const parser = new SmartOffice.Parsers.ExcelParser();
                    const types = parser.getSupportedTypes();
                    const result = `Excel解析器工作正常，支持格式: ${types.join(', ')}`;
                    showResult('parser-result', result);
                    updateStatus('parser-status', 'success');
                } else {
                    throw new Error('ExcelParser 未找到');
                }
            } catch (error) {
                showResult('parser-result', '错误: ' + error.message);
                updateStatus('parser-status', 'error');
            }
        }

        // 4. 测试JSON解析器
        function testJSONParser() {
            updateStatus('parser-status', 'pending');
            try {
                if (SmartOffice.Parsers && SmartOffice.Parsers.JSONParser) {
                    const parser = new SmartOffice.Parsers.JSONParser();
                    const types = parser.getSupportedTypes();
                    const result = `JSON解析器工作正常，支持格式: ${types.join(', ')}`;
                    showResult('parser-result', result);
                    updateStatus('parser-status', 'success');
                } else {
                    throw new Error('JSONParser 未找到');
                }
            } catch (error) {
                showResult('parser-result', '错误: ' + error.message);
                updateStatus('parser-status', 'error');
            }
        }

        // 5. 测试数据筛选
        function testDataFilters() {
            updateStatus('filter-status', 'pending');
            try {
                if (SmartOffice.Filters && SmartOffice.Filters.DataFilters) {
                    const filters = new SmartOffice.Filters.DataFilters();
                    const operators = filters.getSupportedOperators();
                    const result = `数据筛选器工作正常，支持操作符数量: ${operators.length}`;
                    showResult('filter-result', result);
                    updateStatus('filter-status', 'success');
                } else {
                    throw new Error('DataFilters 未找到');
                }
            } catch (error) {
                showResult('filter-result', '错误: ' + error.message);
                updateStatus('filter-status', 'error');
            }
        }

        // 6. 测试条件格式化
        function testFormatter() {
            updateStatus('formatter-status', 'pending');
            try {
                if (SmartOffice.Visualization && SmartOffice.Visualization.Formatter) {
                    const formatter = new SmartOffice.Visualization.Formatter();
                    const schemes = formatter.getColorSchemes();
                    const result = `条件格式化器工作正常，颜色方案数量: ${Object.keys(schemes).length}`;
                    showResult('formatter-result', result);
                    updateStatus('formatter-status', 'success');
                } else {
                    throw new Error('Formatter 未找到');
                }
            } catch (error) {
                showResult('formatter-result', '错误: ' + error.message);
                updateStatus('formatter-status', 'error');
            }
        }

        // 7. 测试图表引擎
        function testChartEngine() {
            updateStatus('chart-status', 'pending');
            try {
                if (SmartOffice.Visualization && SmartOffice.Visualization.ChartEngine) {
                    const engine = new SmartOffice.Visualization.ChartEngine();
                    const types = engine.getChartTypes();
                    
                    // 创建测试图表
                    const testData = [
                        { label: '产品A', value: 30 },
                        { label: '产品B', value: 45 },
                        { label: '产品C', value: 25 },
                        { label: '产品D', value: 60 }
                    ];
                    
                    const chartId = engine.createChart('test-chart', 'bar', testData, {
                        width: 400,
                        height: 250
                    });
                    
                    const result = `图表引擎工作正常，支持图表类型: ${Object.keys(types).length}个，测试图表ID: ${chartId}`;
                    showResult('chart-result', result);
                    updateStatus('chart-status', 'success');
                } else {
                    throw new Error('ChartEngine 未找到');
                }
            } catch (error) {
                showResult('chart-result', '错误: ' + error.message);
                updateStatus('chart-status', 'error');
            }
        }

        // 检查依赖
        function checkDependencies() {
            const dependencies = [
                'SmartOffice',
                'SmartOffice.Core.OfflineManager',
                'SmartOffice.Templates.TemplateManager',
                'SmartOffice.Parsers.ExcelParser',
                'SmartOffice.Parsers.JSONParser',
                'SmartOffice.Filters.DataFilters',
                'SmartOffice.Visualization.Formatter',
                'SmartOffice.Visualization.ChartEngine'
            ];

            const results = dependencies.map(dep => {
                const exists = eval(`typeof ${dep} !== 'undefined'`);
                return `${dep}: ${exists ? '✅' : '❌'}`;
            });

            showResult('overall-result', results.join('\n'));
        }

        // 运行所有测试
        function runAllTests() {
            setTimeout(() => testOfflineFeatures(), 100);
            setTimeout(() => testTemplateManager(), 200);
            setTimeout(() => testExcelParser(), 300);
            setTimeout(() => testJSONParser(), 400);
            setTimeout(() => testDataFilters(), 500);
            setTimeout(() => testFormatter(), 600);
            setTimeout(() => testChartEngine(), 700);
        }

        // 页面加载完成后检查依赖
        window.addEventListener('load', function() {
            console.log('🚀 增强功能测试页面加载完成');
            checkDependencies();
        });
    </script>
</body>
</html>
