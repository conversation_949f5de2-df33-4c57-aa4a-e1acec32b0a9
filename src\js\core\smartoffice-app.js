/**
 * @file SmartOffice应用主控制器
 * @description 应用的主要控制逻辑和生命周期管理
 * <AUTHOR> Team
 */

/**
 * @function App
 * @description 应用主控制器构造函数
 * @constructor
 */
function App() {
    /**
     * @property {boolean} initialized - 是否已初始化
     */
    this.initialized = false;
    
    /**
     * @property {Object} components - 组件实例集合
     */
    this.components = {};
    
    /**
     * @property {Object} eventBus - 事件总线引用
     */
    this.eventBus = SmartOffice.Core.EventBus;
    
    /**
     * @property {Object} storage - 存储管理器引用
     */
    this.storage = SmartOffice.Core.Storage;
    
    /**
     * @property {Object} helpers - 工具函数引用
     */
    this.helpers = SmartOffice.Utils.Helpers;
    
    /**
     * @property {Object} dom - DOM工具引用
     */
    this.dom = SmartOffice.Utils.DOM;
    
    SmartOffice.log('info', 'App主控制器实例创建完成');
}

/**
 * @function App.prototype.init
 * @description 初始化应用
 */
App.prototype.init = function() {
    if (this.initialized) {
        SmartOffice.log('warn', '应用已经初始化过了');
        return;
    }
    
    try {
        // 检查依赖
        this.checkDependencies();
        
        // 初始化UI元素
        this.initializeUI();
        
        // 初始化组件
        this.initializeComponents();
        
        // 绑定全局事件
        this.bindGlobalEvents();
        
        // 设置初始状态
        this.setInitialState();
        
        // 标记为已初始化
        this.initialized = true;
        
        // 触发应用就绪事件
        this.eventBus.emit(SmartOffice.Events.APP_READY);
        
        SmartOffice.log('info', '🚀 SmartOffice应用初始化完成');
        
    } catch (error) {
        SmartOffice.log('error', '应用初始化失败:', error);
        this.eventBus.emit(SmartOffice.Events.APP_ERROR, error);
        throw error;
    }
};

/**
 * @function App.prototype.checkDependencies
 * @description 检查必要的依赖是否已加载
 * @private
 */
App.prototype.checkDependencies = function() {
    if (!SmartOffice.ready()) {
        throw new Error('SmartOffice模块未完全加载');
    }
    
    // 检查必要的DOM元素
    const requiredElements = [
        '#app',
        '#mainContent',
        '#configListPage',
        '#configFormPage',
        '#configList',
        '#navTitle',
        '#navAdd',
        '#navBack'
    ];
    
    for (let i = 0; i < requiredElements.length; i++) {
        const element = this.dom.querySelector(requiredElements[i]);
        if (!element) {
            throw new Error('必要的DOM元素不存在: ' + requiredElements[i]);
        }
    }
    
    SmartOffice.log('info', '依赖检查通过');
};

/**
 * @function App.prototype.initializeUI
 * @description 初始化UI元素
 * @private
 */
App.prototype.initializeUI = function() {
    // 获取主要UI元素的引用
    this.elements = {
        app: this.dom.querySelector('#app'),
        mainContent: this.dom.querySelector('#mainContent'),
        configListPage: this.dom.querySelector('#configListPage'),
        configFormPage: this.dom.querySelector('#configFormPage'),
        configList: this.dom.querySelector('#configList'),
        emptyState: this.dom.querySelector('#emptyState'),
        navTitle: this.dom.querySelector('#navTitle'),
        navAdd: this.dom.querySelector('#navAdd'),
        navBack: this.dom.querySelector('#navBack'),
        loadingOverlay: this.dom.querySelector('#loadingOverlay'),
        toastContainer: this.dom.querySelector('#toastContainer')
    };
    
    // 设置初始UI状态
    this.updateNavigation();
    
    SmartOffice.log('info', 'UI元素初始化完成');
};

/**
 * @function App.prototype.initializeComponents
 * @description 初始化应用组件
 * @private
 */
App.prototype.initializeComponents = function() {
    try {
        // 初始化配置列表组件
        if (SmartOffice.Components.ConfigList && this.elements.configList) {
            this.components.configList = new SmartOffice.Components.ConfigList(this.elements.configList);
            SmartOffice.log('info', '配置列表组件初始化完成');
        }

        // 初始化配置表单组件
        if (SmartOffice.Components.ConfigForm && this.elements.configFormPage) {
            // 直接使用配置表单页面容器，不需要查找子元素
            this.components.configForm = new SmartOffice.Components.ConfigForm(this.elements.configFormPage);
            SmartOffice.log('info', '配置表单组件初始化完成');
        }

        // 后续会添加其他组件的初始化
        // this.components.dropdown = new SmartOffice.Components.Dropdown(...);
        
    } catch (error) {
        SmartOffice.log('error', '组件初始化失败:', error);
        throw error;
    }
};

/**
 * @function App.prototype.bindGlobalEvents
 * @description 绑定全局事件监听器
 * @private
 */
App.prototype.bindGlobalEvents = function() {
    const self = this;
    
    // 导航按钮事件
    if (this.elements.navAdd) {
        this.elements.navAdd.addEventListener('click', function() {
            self.handleAddConfig();
        });
    }
    
    if (this.elements.navBack) {
        this.elements.navBack.addEventListener('click', function() {
            self.handleBackNavigation();
        });
    }
    
    // 应用级事件监听
    this.eventBus.on(SmartOffice.Events.PAGE_CHANGE, function(pageName) {
        self.handlePageChange(pageName);
    });
    
    this.eventBus.on(SmartOffice.Events.CONFIG_SELECT, function(config) {
        self.handleConfigSelect(config);
    });
    
    this.eventBus.on(SmartOffice.Events.UI_LOADING_SHOW, function() {
        self.showLoading();
    });
    
    this.eventBus.on(SmartOffice.Events.UI_LOADING_HIDE, function() {
        self.hideLoading();
    });
    
    this.eventBus.on(SmartOffice.Events.UI_TOAST_SHOW, function(message, type) {
        self.showToast(message, type);
    });
    
    // iOS特定事件处理
    if (this.helpers.isIOS()) {
        // 处理iOS Safari的特殊情况
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                self.handleOrientationChange();
            }, 100);
        });
    }
    
    SmartOffice.log('info', '全局事件绑定完成');
};

/**
 * @function App.prototype.setInitialState
 * @description 设置应用初始状态
 * @private
 */
App.prototype.setInitialState = function() {
    // 导航到初始页面（配置列表）
    SmartOffice.Core.Router.navigate('configList', null, false);

    // 隐藏加载状态
    this.hideLoading();

    SmartOffice.log('info', '初始状态设置完成');
};

/**
 * @function App.prototype.handleAddConfig
 * @description 处理添加配置按钮点击
 */
App.prototype.handleAddConfig = function() {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 导航到配置表单页面（新建模式）
    SmartOffice.Core.Router.navigate('configForm');

    SmartOffice.log('info', '开始创建新配置');
};

/**
 * @function App.prototype.handleBackNavigation
 * @description 处理返回按钮点击
 */
App.prototype.handleBackNavigation = function() {
    // 触发触觉反馈
    SmartOffice.triggerHapticFeedback('light');

    // 使用路由器返回上一页
    SmartOffice.Core.Router.back();
};

/**
 * @function App.prototype.handlePageChange
 * @description 处理页面切换
 * @param {string} pageName - 页面名称
 */
App.prototype.handlePageChange = function(pageName) {
    SmartOffice.State.currentPage = pageName;
    this.updateNavigation();
    
    SmartOffice.log('info', '页面切换到: ' + pageName);
};

/**
 * @function App.prototype.handleConfigSelect
 * @description 处理配置选择
 * @param {Object} config - 选中的配置
 */
App.prototype.handleConfigSelect = function(config) {
    SmartOffice.State.currentConfig = config;

    // 导航到配置表单页面（编辑模式）
    SmartOffice.Core.Router.navigate('configForm', { config: config });

    SmartOffice.log('info', '开始编辑配置: ' + config.name);
};

/**
 * @function App.prototype.navigateToPage
 * @description 导航到指定页面
 * @param {string} pageName - 页面名称
 */
App.prototype.navigateToPage = function(pageName) {
    this.eventBus.emit(SmartOffice.Events.PAGE_CHANGE, pageName);
};

/**
 * @function App.prototype.updateNavigation
 * @description 更新导航栏状态
 */
App.prototype.updateNavigation = function() {
    const currentPage = SmartOffice.State.currentPage;
    
    // 更新标题
    if (this.elements.navTitle) {
        switch (currentPage) {
            case 'configList':
                this.elements.navTitle.textContent = '透视分析';
                break;
            case 'configForm':
                this.elements.navTitle.textContent = '配置透视表';
                break;
            default:
                this.elements.navTitle.textContent = 'SmartOffice';
        }
    }
    
    // 更新返回按钮显示状态
    if (this.elements.navBack) {
        if (currentPage === 'configList') {
            this.dom.hide(this.elements.navBack);
        } else {
            this.dom.show(this.elements.navBack);
        }
    }
    
    // 更新添加按钮显示状态
    if (this.elements.navAdd) {
        if (currentPage === 'configList') {
            this.dom.show(this.elements.navAdd);
        } else {
            this.dom.hide(this.elements.navAdd);
        }
    }
};

/**
 * @function App.prototype.showLoading
 * @description 显示加载状态
 */
App.prototype.showLoading = function() {
    if (this.elements.loadingOverlay) {
        this.dom.show(this.elements.loadingOverlay);
    }
    SmartOffice.State.isLoading = true;
};

/**
 * @function App.prototype.hideLoading
 * @description 隐藏加载状态
 */
App.prototype.hideLoading = function() {
    if (this.elements.loadingOverlay) {
        this.dom.hide(this.elements.loadingOverlay);
    }
    SmartOffice.State.isLoading = false;
};

/**
 * @function App.prototype.showToast
 * @description 显示iOS风格提示消息
 * @param {string} message - 消息内容
 * @param {string} type - 消息类型 ('info', 'success', 'warning', 'error')
 */
App.prototype.showToast = function(message, type) {
    if (!this.elements.toastContainer) return;
    
    const toast = this.dom.createElement('div', {
        className: 'ios-toast'
    }, message);
    
    this.elements.toastContainer.appendChild(toast);
    
    // 淡入动画
    this.dom.fadeIn(toast, 200);
    
    // 自动隐藏
    setTimeout(function() {
        toast.style.opacity = '0';
        setTimeout(function() {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 200);
    }, 2000);
};

/**
 * @function App.prototype.handleOrientationChange
 * @description 处理设备方向改变
 * @private
 */
App.prototype.handleOrientationChange = function() {
    // 更新视口高度变量
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', vh + 'px');
    
    SmartOffice.log('info', '设备方向已改变，视口高度已更新');
};

// 创建全局应用实例
SmartOffice.Core.App = new App();

SmartOffice.log('info', 'SmartOffice应用主控制器模块初始化完成');
